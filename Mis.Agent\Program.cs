using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Mis.Agent.ApplicationsContext;
using Mis.Agent.Localization;
using Mis.Agent.PluginSystem;
using Mis.Shared.Interface;
using Serilog;
using Serilog.Events;
using System;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using Volo.Abp;

namespace Mis.Agent
{
    public static class Program
    {
        static IConfiguration configuration;

        [STAThread]
        static void Main()
        {
            Log.Logger = new LoggerConfiguration()
#if DEBUG
                .MinimumLevel.Debug()
#else
                .MinimumLevel.Information()
#endif
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .WriteTo.Async(c => c.File("Logs/logs.txt"))
                .WriteTo.Async(c => c.Console())
                .CreateLogger();

            var exePath = AppContext.BaseDirectory;
            var configPath = Path.Combine(exePath, "appsettings.json");

            if (!File.Exists(configPath))
            {
                MessageBox.Show($"Configuration file not found: {configPath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }

            configuration = new ConfigurationBuilder()
                .SetBasePath(exePath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Initialize localization early to ensure Arabic is set before plugins load
            var localizationService = GlobalLocalizationService.Instance;

            var baseUrl = configuration.GetSection("Server").GetValue<string>("BaseUrl");
            var barcodeBaseUrl = configuration.GetSection("Barcode").GetValue<string>("BarcodeBaseUrl");
            if (string.IsNullOrWhiteSpace(baseUrl))
            {
                throw new UserFriendlyException("Base Url Is Empty");
            }
            var pluginManager = PluginManager.Instance;
            string pluginPath = Path.Combine(AppContext.BaseDirectory, "Plugins");

            // 1. Discover plugins early
            pluginManager.DiscoverPlugins(pluginPath);

            // 2. Load plugins early
            //pluginManager.LoadAutoLoadPlugins();


            // 3. Build host
            var host = CreateHostBuilder(baseUrl, barcodeBaseUrl).Build();


            using var serviceScope = host.Services.CreateScope();
            var serviceProvider = serviceScope.ServiceProvider;
            // ? Move plugin setup here
            //var pluginManager = PluginManager.Instance;
            //string pluginPath = Path.Combine(AppContext.BaseDirectory, "Plugins");
            pluginManager.SetServiceProvider(serviceProvider);
            pluginManager.LoadAutoLoadPlugins();


            Task.Run(() => StartHostAsync(host));



            try
            {
                Application.Run(new AgentStarter(serviceProvider));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async Task StartHostAsync(IHost host)
        {
            try
            {
                await host.StartAsync();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host failed to start");
                throw;
            }
        }
        //public static IHostBuilder CreateHostBuilder(string baseUrl, string barcodeBaseUrl)
        //{
        //    var baseUri = new Uri(baseUrl);
        //    var barcodeUri = new Uri(barcodeBaseUrl);

        //    // ? Gracefully resolve IP or fallback to localhost
        //    IPAddress baseIp = IPAddress.TryParse(baseUri.Host, out var parsedBaseIp)
        //        ? parsedBaseIp
        //        : IPAddress.Loopback;

        //    IPAddress barcodeIp = IPAddress.TryParse(barcodeUri.Host, out var parsedBarcodeIp)
        //        ? parsedBarcodeIp
        //        : IPAddress.Loopback;

        //    int basePort = baseUri.Port;
        //    int barcodePort = barcodeUri.Port;

        //    // ?? Check if ports are in use before binding
        //    if (IsPortInUse(basePort))
        //        throw new InvalidOperationException($"Port {basePort} is already in use.");

        //    if (IsPortInUse(barcodePort))
        //        throw new InvalidOperationException($"Port {barcodePort} is already in use.");

        //    Console.WriteLine($"? Binding WebApp at {baseIp}:{basePort}");
        //    Console.WriteLine($"? Binding BarcodeHub at {barcodeIp}:{barcodePort}");

        //    return Host.CreateDefaultBuilder()
        //        .UseSerilog((context, config) =>
        //            config.ReadFrom.Configuration(context.Configuration)
        //                  .WriteTo.Console()
        //                  .WriteTo.File("logs/log.txt", rollingInterval: RollingInterval.Day))
        //        .ConfigureWebHostDefaults(webBuilder =>
        //        {
        //            webBuilder.UseStartup<Startup>();
        //            webBuilder.ConfigureKestrel(options =>
        //            {
        //                options.Listen(baseIp, basePort);
        //                options.Listen(barcodeIp, barcodePort);
        //            });
        //        });
        //}
        public static IHostBuilder CreateHostBuilder(string baseUrl, string barcodeBaseUrl)
        {
            var baseUri = new Uri(baseUrl);
            var barcodeUri = new Uri(barcodeBaseUrl);

            // Resolve IPs
            IPAddress baseIp = IPAddress.TryParse(baseUri.Host, out var parsedBaseIp)
                ? parsedBaseIp
                : IPAddress.Loopback;

            IPAddress barcodeIp = IPAddress.TryParse(barcodeUri.Host, out var parsedBarcodeIp)
                ? parsedBarcodeIp
                : IPAddress.Loopback;

            int basePort = baseUri.Port;
            int barcodePort = barcodeUri.Port;

            if (IsPortInUse(basePort))
                throw new InvalidOperationException($"Port {basePort} is already in use.");

            if (IsPortInUse(barcodePort))
                throw new InvalidOperationException($"Port {barcodePort} is already in use.");

            Console.WriteLine($"? Binding WebApp at {baseIp}:{basePort}");
            Console.WriteLine($"? Binding BarcodeHub at {barcodeIp}:{barcodePort}");

            // Read cert config
            var certSection = configuration.GetSection("Server").GetSection("Certificate");
            string certPath = Path.Combine(AppContext.BaseDirectory, certSection["Path"]);
            string certPassword = certSection["Password"];

            if (!File.Exists(certPath))
            {
                MessageBox.Show($"Certificate not found: {certPath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }

            var certificate = new X509Certificate2(
                certPath,
                certPassword,
                X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable);

            return Host.CreateDefaultBuilder()
                .UseSerilog((context, config) =>
                    config.ReadFrom.Configuration(context.Configuration)
                          .WriteTo.Console()
                          .WriteTo.File("logs/log.txt", rollingInterval: RollingInterval.Day, retainedFileCountLimit: 5))
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.ConfigureKestrel(options =>
                    {
                        // WebApp with HTTPS
                        options.Listen(baseIp, basePort, listenOptions =>
                        {
                            listenOptions.UseHttps(certificate);
                        });

                        // BarcodeHub - HTTP or HTTPS as needed
                        options.Listen(barcodeIp, barcodePort);
                    });
                });
        }

        private static bool IsPortInUse(int port)
        {
            bool inUse = false;
            try
            {
                var listener = new TcpListener(IPAddress.Loopback, port);
                listener.Start();
                listener.Stop();
            }
            catch (SocketException)
            {
                inUse = true;
            }
            return inUse;
        }

    }
}
