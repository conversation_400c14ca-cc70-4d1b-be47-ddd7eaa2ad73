﻿using Mis.Shared.Interface;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TransactionDto = Mis.Shared.Interface.TransactionDto;

namespace Mis.Agent.Print
{
    [ApiController]
    [Route("[controller]")]
    [EnableCors("AllowAll")]
    public class PrintController:ControllerBase
    {
        private readonly IPrintAppService _printAppService;
        private readonly INotificationAppService _notificationAppService;
        public PrintController(
            IPrintAppService printAppService,
            INotificationAppService notificationAppService)
        {
            _printAppService = printAppService;
            _notificationAppService = notificationAppService;
        }
        [HttpPost("PrintAsync")]
        public async Task<ActionResult> PrintAsync(TransactionDto input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.HtmlContent))
                {
                    return StatusCode(StatusCodes.Status204NoContent, "HTML content is empty.");

                }


                if (_notificationAppService == null || !_notificationAppService.DatabaseFileExists())
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, "Database file does not exist.");
                }

                if (!_notificationAppService.TableExists("Notifications"))
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, "Notifications table does not exist.");
                }

                input.IsPrinted = false;

                await _notificationAppService.SaveNotificationAsync(input);

                try
                {
                  bool isPrinted  = _printAppService.PrintAsync(input);

                    // Wait for the print job to complete
                    // Update the print status in the database
                    await _notificationAppService.UpdatePrintStatusAsync(input.Id, isPrinted);
                    _printAppService.ShowNotification("Print Notification", isPrinted ? "Print job completed successfully." : "Print job failed or was canceled.");
                    await _notificationAppService.PerformCleanupAsync();
                    if (isPrinted)
                    {
                        return Ok("Print job completed successfully.");
                    }
                    else
                    {
                        return StatusCode(StatusCodes.Status500InternalServerError, "Print job failed or was canceled.");
                    }
                }
                catch (Exception ex)
                {
                    // Handle any exceptions from task completion
                    return StatusCode(StatusCodes.Status500InternalServerError, $"An error occurred while processing the print job: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions in the controller
                return StatusCode(StatusCodes.Status500InternalServerError, $"An error occurred: {ex.Message}");
            }

        }
    }
}
