﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Mis.Agent.Barcode.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class BarcodeResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal BarcodeResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Mis.Agent.Barcode.Resources.BarcodeResources", typeof(BarcodeResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Ports:.
        /// </summary>
        internal static string AvailablePortsLabel {
            get {
                return ResourceManager.GetString("AvailablePortsLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Scanners:.
        /// </summary>
        internal static string AvailableScannersLabel {
            get {
                return ResourceManager.GetString("AvailableScannersLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Settings Error.
        /// </summary>
        internal static string BarcodeError {
            get {
                return ResourceManager.GetString("BarcodeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Configuration.
        /// </summary>
        internal static string BarcodeFormTitle {
            get {
                return ResourceManager.GetString("BarcodeFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Success.
        /// </summary>
        internal static string BarcodeSuccess {
            get {
                return ResourceManager.GetString("BarcodeSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Settings.
        /// </summary>
        internal static string BarcodeTabText {
            get {
                return ResourceManager.GetString("BarcodeTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode URL:.
        /// </summary>
        internal static string BarcodeUrlLabel {
            get {
                return ResourceManager.GetString("BarcodeUrlLabel", resourceCulture);
            }
        }
        internal static string BarcodePortLabel
        {
            get
            {
                return ResourceManager.GetString("BarcodePortLabel", resourceCulture);
            }
        }
        internal static string BarcodeIpLabel
        {
            get
            {
                return ResourceManager.GetString("BarcodeIpLabel", resourceCulture);
            }
        }
        internal static string BarcodeProtocolLabel
        {
            get
            {
                return ResourceManager.GetString("BarcodeProtocolLabel", resourceCulture);
            }
        }
        
            
        /// <summary>
        ///   Looks up a localized string similar to COM Port:.
        /// </summary>
        internal static string ComPortLabel {
            get {
                return ResourceManager.GetString("ComPortLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save configuration. Please try again..
        /// </summary>
        internal static string ConfigurationFailedMessage {
            get {
                return ResourceManager.GetString("ConfigurationFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration saved successfully!.
        /// </summary>
        internal static string ConfigurationSavedMessage {
            get {
                return ResourceManager.GetString("ConfigurationSavedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection test failed. Please check your settings..
        /// </summary>
        internal static string ConnectionFailedMessage {
            get {
                return ResourceManager.GetString("ConnectionFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection test successful!.
        /// </summary>
        internal static string ConnectionSuccessMessage {
            get {
                return ResourceManager.GetString("ConnectionSuccessMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to send manual data..
        /// </summary>
        internal static string DataSendFailedMessage {
            get {
                return ResourceManager.GetString("DataSendFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual data sent successfully!.
        /// </summary>
        internal static string DataSentMessage {
            get {
                return ResourceManager.GetString("DataSentMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Connection Failed.
        /// </summary>
        internal static string HubConnectionFailed {
            get {
                return ResourceManager.GetString("HubConnectionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Connection Success.
        /// </summary>
        internal static string HubConnectionSuccess {
            get {
                return ResourceManager.GetString("HubConnectionSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid URL..
        /// </summary>
        internal static string InvalidUrlMessage {
            get {
                return ResourceManager.GetString("InvalidUrlMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a COM port..
        /// </summary>
        internal static string NoPortSelectedMessage {
            get {
                return ResourceManager.GetString("NoPortSelectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a scanner..
        /// </summary>
        internal static string NoScannerSelectedMessage {
            get {
                return ResourceManager.GetString("NoScannerSelectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Barcode Configuration.
        /// </summary>
        internal static string SaveBarcodeConfigButton {
            get {
                return ResourceManager.GetString("SaveBarcodeConfigButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Scanner Configuration.
        /// </summary>
        internal static string SaveScannerConfigButton {
            get {
                return ResourceManager.GetString("SaveScannerConfigButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner Configuration.
        /// </summary>
        internal static string ScannerFormTitle {
            get {
                return ResourceManager.GetString("ScannerFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scanner Settings.
        /// </summary>
        internal static string ScannerTabText {
            get {
                return ResourceManager.GetString("ScannerTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Manual Data.
        /// </summary>
        internal static string SendManualDataButton {
            get {
                return ResourceManager.GetString("SendManualDataButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Connection.
        /// </summary>
        internal static string TestConnectionButton {
            get {
                return ResourceManager.GetString("TestConnectionButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use Barcode Reader.
        /// </summary>
        internal static string UseBarcodeReaderCheckbox {
            get {
                return ResourceManager.GetString("UseBarcodeReaderCheckbox", resourceCulture);
            }
        }
    }
}
