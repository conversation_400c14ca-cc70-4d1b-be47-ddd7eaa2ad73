<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing"
  mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework
  object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0,
      Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0,
      Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BarcodeFormTitle" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="ScannerFormTitle" xml:space="preserve">
    <value>إعدادات الماسح الضوئي</value>
  </data>
  <data name="BarcodeTabText" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="ScannerTabText" xml:space="preserve">
    <value>إعدادات الماسح</value>
  </data>
  <data name="BarcodeUrlLabel" xml:space="preserve">
    <value>رابط الباركود:</value>
  </data>
  <data name="ComPortLabel" xml:space="preserve">
    <value>منفذ COM:</value>
  </data>
  <data name="AvailablePortsLabel" xml:space="preserve">
    <value>المنافذ المتاحة:</value>
  </data>
  <data name="AvailableScannersLabel" xml:space="preserve">
    <value>الماسحات المتاحة:</value>
  </data>
  <data name="SaveBarcodeConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الباركود</value>
  </data>
  <data name="SaveScannerConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الماسح</value>
  </data>
  <data name="TestConnectionButton" xml:space="preserve">
    <value>اختبار الاتصال</value>
  </data>
  <data name="SendManualDataButton" xml:space="preserve">
    <value>إرسال البيانات يدوياً</value>
  </data>
  <data name="UseBarcodeReaderCheckbox" xml:space="preserve">
    <value>استخدام قارئ الباركود</value>
  </data>
  <data name="ConfigurationSavedMessage" xml:space="preserve">
    <value>تم حفظ الإعدادات بنجاح!</value>
  </data>
  <data name="ConfigurationFailedMessage" xml:space="preserve">
    <value>فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.</value>
  </data>
  <data name="ConnectionSuccessMessage" xml:space="preserve">
    <value>نجح اختبار الاتصال!</value>
  </data>
  <data name="ConnectionFailedMessage" xml:space="preserve">
    <value>فشل اختبار الاتصال. يرجى التحقق من الإعدادات.</value>
  </data>
  <data name="InvalidUrlMessage" xml:space="preserve">
    <value>يرجى إدخال رابط صحيح.</value>
  </data>
  <data name="NoPortSelectedMessage" xml:space="preserve">
    <value>يرجى اختيار منفذ COM.</value>
  </data>
  <data name="NoScannerSelectedMessage" xml:space="preserve">
    <value>يرجى اختيار ماسح ضوئي.</value>
  </data>
  <data name="DataSentMessage" xml:space="preserve">
    <value>تم إرسال البيانات اليدوية بنجاح!</value>
  </data>
  <data name="DataSendFailedMessage" xml:space="preserve">
    <value>فشل في إرسال البيانات اليدوية.</value>
  </data>
  <data name="BarcodeError" xml:space="preserve">
    <value>خطأ في إعدادات الباركود</value>
  </data>
  <data name="HubConnectionFailed" xml:space="preserve">
    <value>تعذر الاتصال بـ Hub. تحقق من عنوان الرابط أو الاتصال.</value>
  </data>
  <data name="BarcodeSuccess" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="HubConnectionSuccess" xml:space="preserve">
    <value>تم الاتصال ب HubbConnection بنجاح .</value>
  </data>
  <data name="BarcodeInitializedMessage" xml:space="preserve">
    <value>تم تهيئة الباركود بنجاح</value>
  </data>
  <data name="BarcodeInitializationFailed" xml:space="preserve">
    <value>فشل في تهيئة الباركود</value>
  </data>
  <data name="BarcodeExceptionMessage" xml:space="preserve">
    <value>حدث استثناء أثناء تهيئة الباركود</value>
  </data>
  <data name="ScanningFailedMessage" xml:space="preserve">
    <value>فشل المسح. لم يتم استلام بيانات الصورة</value>
  </data>
  <data name="BarcodePortLabel" xml:space="preserve">
    <value>منفذ عنوان الباركود</value>
  </data>
  <data name="BarcodeIpLabel" xml:space="preserve">
    <value>الباركود IP</value>
  </data>
  <data name="BarcodeProtocolLabel" xml:space="preserve">
    <value>بروتوكول الباركود</value>
  </data>
</root>