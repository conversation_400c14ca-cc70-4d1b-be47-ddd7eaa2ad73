{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Mis.Agent.Port/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "9.0.6", "Microsoft.AspNetCore.Owin": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting": "9.0.6", "Microsoft.Windows.Compatibility": "9.0.6", "Mis.Shared.Interface": "1.0.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Async": "2.1.0", "Swashbuckle.AspNetCore": "6.8.0", "System.Data.SQLite": "1.0.119", "System.Security.Cryptography.Pkcs": "9.0.6", "System.Security.Cryptography.X509Certificates": "4.3.2", "Volo.Abp.Autofac": "9.0.6"}, "runtime": {"Mis.Agent.Port.dll": {}}, "resources": {"ar-SA/Mis.Agent.Port.resources.dll": {"locale": "ar-SA"}}}, "Autofac/8.1.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac.Extensions.DependencyInjection/10.0.0": {"dependencies": {"Autofac": "8.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.0.0"}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"dependencies": {"Autofac": "8.1.0", "Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "9.0.6"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EntityFramework/6.4.4": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.CodeDom": "9.0.6", "System.ComponentModel.Annotations": "4.7.0", "System.Configuration.ConfigurationManager": "9.0.6", "System.Data.SqlClient": "4.9.0"}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}, "lib/netstandard2.1/EntityFramework.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}}}, "JetBrains.Annotations/2024.2.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "202*******"}}}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Features": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Net.ServerSentEvents": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.JsonPatch/9.0.6": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/9.0.6": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "9.0.6", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Owin/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Owin.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Client/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "9.0.6", "Microsoft.AspNetCore.SignalR.Client.Core": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.6": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.6", "Microsoft.AspNetCore.SignalR.Protocols.Json": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "System.Threading.Channels": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Common/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.6": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Features/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.CommandLine": "9.0.6", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Logging.Console": "9.0.6", "Microsoft.Extensions.Logging.Debug": "9.0.6", "Microsoft.Extensions.Logging.EventLog": "9.0.6", "Microsoft.Extensions.Logging.EventSource": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Localization/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Localization.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Microsoft.Extensions.Localization.Abstractions/9.0.0": {}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Console/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.EventLog": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.ObjectPool/5.0.10": {}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.14.0", "fileVersion": "1.6.14.0"}}}, "Microsoft.Win32.Registry.AccessControl/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Win32.SystemEvents/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Windows.Compatibility/9.0.6": {"dependencies": {"Microsoft.Win32.Registry.AccessControl": "9.0.6", "Microsoft.Win32.SystemEvents": "9.0.6", "System.CodeDom": "9.0.6", "System.ComponentModel.Composition": "9.0.6", "System.ComponentModel.Composition.Registration": "9.0.6", "System.Configuration.ConfigurationManager": "9.0.6", "System.Data.Odbc": "9.0.6", "System.Data.OleDb": "9.0.6", "System.Data.SqlClient": "4.9.0", "System.Diagnostics.EventLog": "9.0.6", "System.Diagnostics.PerformanceCounter": "9.0.6", "System.DirectoryServices": "9.0.6", "System.DirectoryServices.AccountManagement": "9.0.6", "System.DirectoryServices.Protocols": "9.0.6", "System.Drawing.Common": "9.0.6", "System.IO.Packaging": "9.0.6", "System.IO.Ports": "9.0.6", "System.Management": "9.0.6", "System.Reflection.Context": "9.0.6", "System.Runtime.Caching": "9.0.6", "System.Security.Cryptography.Pkcs": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.6", "System.Security.Cryptography.Xml": "9.0.6", "System.Security.Permissions": "9.0.6", "System.Security.Principal.Windows": "5.0.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.ServiceModel.Syndication": "9.0.6", "System.ServiceProcess.ServiceController": "9.0.6", "System.Speech": "9.0.6", "System.Text.Encoding.CodePages": "9.0.6", "System.Threading.AccessControl": "9.0.6", "System.Web.Services.Description": "4.10.3"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "9.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/9.0.6": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.6", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.6"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/4.2.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.AspNetCore/9.0.0": {"dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.6", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Async/2.1.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}}}, "Swashbuckle.AspNetCore/6.8.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.8.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.8.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.8.0"}}, "Swashbuckle.AspNetCore.Swagger/6.8.0": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.744"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.8.0"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.744"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.0": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.744"}}}, "System.CodeDom/9.0.6": {"runtime": {"lib/net9.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/9.0.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.ComponentModel.Composition/9.0.6": {"runtime": {"lib/net9.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.ComponentModel.Composition.Registration/9.0.6": {"dependencies": {"System.ComponentModel.Composition": "9.0.6", "System.Reflection.Context": "9.0.6"}, "runtime": {"lib/net9.0/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Configuration.ConfigurationManager/9.0.6": {"dependencies": {"System.Diagnostics.EventLog": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.6"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Data.Odbc/9.0.6": {"runtime": {"lib/net9.0/System.Data.Odbc.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.Data.Odbc.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}, "runtimes/win/lib/net9.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Data.OleDb/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6", "System.Diagnostics.PerformanceCounter": "9.0.6"}, "runtime": {"lib/net9.0/System.Data.OleDb.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Data.SqlClient/4.9.0": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/net8.0/System.Data.SqlClient.dll": {"assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}, "runtimes/win/lib/net8.0/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.6.1.6", "fileVersion": "4.900.24.56208"}}}, "System.Data.SQLite/1.0.119": {"dependencies": {"System.Data.SQLite.Core": "1.0.119", "System.Data.SQLite.EF6": "1.0.119"}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Data.SQLite.EF6/1.0.119": {"dependencies": {"EntityFramework": "6.4.4"}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/9.0.6": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Diagnostics.PerformanceCounter/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6"}, "runtime": {"lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.DirectoryServices/9.0.6": {"runtime": {"lib/net9.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.DirectoryServices.AccountManagement/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6", "System.DirectoryServices": "9.0.6", "System.DirectoryServices.Protocols": "9.0.6"}, "runtime": {"lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.DirectoryServices.Protocols/9.0.6": {"runtime": {"lib/net9.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}, "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}, "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Drawing.Common/9.0.6": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.6"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Packaging/9.0.6": {"runtime": {"lib/net9.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.IO.Ports/9.0.6": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.6"}, "runtime": {"lib/net9.0/System.IO.Ports.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}, "runtimes/win/lib/net9.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.4.5": {"runtime": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.4.5.0", "fileVersion": "1.4.5.0"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Management/9.0.6": {"dependencies": {"System.CodeDom": "9.0.6"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Net.ServerSentEvents/9.0.6": {"runtime": {"lib/net9.0/System.Net.ServerSentEvents.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.ServiceModel/4.10.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "9.0.6", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Context/9.0.6": {"runtime": {"lib/net9.0/System.Reflection.Context.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/9.0.6": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.6"}, "runtime": {"lib/net9.0/System.Runtime.Caching.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/9.0.6": {"runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/9.0.6": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.Cryptography.X509Certificates/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/9.0.6": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.6"}, "runtime": {"lib/net9.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.Permissions/9.0.6": {"dependencies": {"System.Windows.Extensions": "9.0.6"}, "runtime": {"lib/net9.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Http/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.NetTcp/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Primitives/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Security/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Syndication/9.0.6": {"runtime": {"lib/net9.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.ServiceProcess.ServiceController/9.0.6": {"dependencies": {"System.Diagnostics.EventLog": "9.0.6"}, "runtime": {"lib/net9.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Speech/9.0.6": {"runtime": {"lib/net9.0/System.Speech.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Speech.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/9.0.6": {}, "System.Text.Encodings.Web/9.0.0": {}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.AccessControl/9.0.6": {"runtime": {"lib/net9.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.Channels/9.0.6": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Web.Services.Description/4.10.3": {"runtime": {"lib/netstandard2.0/System.Web.Services.Description.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Windows.Extensions/9.0.6": {"runtime": {"lib/net9.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Volo.Abp.Autofac/9.0.6": {"dependencies": {"Autofac": "8.1.0", "Autofac.Extensions.DependencyInjection": "10.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Volo.Abp.Castle.Core": "9.0.6"}, "runtime": {"lib/net9.0/Volo.Abp.Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Castle.Core/9.0.6": {"dependencies": {"Castle.Core": "5.1.1", "Castle.Core.AsyncInterceptor": "2.1.0", "Volo.Abp.Core": "9.0.6"}, "runtime": {"lib/net9.0/Volo.Abp.Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/9.0.6": {"dependencies": {"JetBrains.Annotations": "2024.2.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.6", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.6", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Localization": "9.0.0", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "9.0.0", "System.Linq.Dynamic.Core": "1.4.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net9.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mis.Shared.Interface/1.0.0": {"dependencies": {"Microsoft.AspNetCore.SignalR.Client": "9.0.6", "Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting": "9.0.6", "System.Data.SQLite": "1.0.119", "System.Drawing.Common": "9.0.6", "System.IO.Ports": "9.0.6"}, "runtime": {"Mis.Shared.Interface.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Mis.Agent.Port/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Autofac/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-O2QT+0DSTBR2Ojpacmcj3L0KrnnXTFrwLl/OW1lBUDiHhb89msHEHNhTA8AlK3jdFiAfMbAYyQaJVvRe6oSBcQ==", "path": "autofac/8.1.0", "hashPath": "autofac.8.1.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/10.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZjR/onUlP7BzQ7VBBigQepWLAyAzi3VRGX3pP6sBqkPRiT61fsTZqbTpRUKxo30TMgbs1o3y6bpLbETix4SJog==", "path": "autofac.extensions.dependencyinjection/10.0.0", "hashPath": "autofac.extensions.dependencyinjection.10.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "path": "autofac.extras.dynamicproxy/7.1.0", "hashPath": "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "EntityFramework/6.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "path": "entityframework/6.4.4", "hashPath": "entityframework.6.4.4.nupkg.sha512"}, "JetBrains.Annotations/2024.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GNnqCFW/163p1fOehKx0CnAqjmpPrUSqrgfHM6qca+P+RN39C9rhlfZHQpJhxmQG/dkOYe/b3Z0P8b6Kv5m1qw==", "path": "jetbrains.annotations/2024.2.0", "hashPath": "jetbrains.annotations.2024.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-78aCqAAdbJvwRMyGp63iVcSobLb+LrZ8EjIU06dXwflBSpUDkicZPppJuoggdvWvc1W8eA62w5Sh7+ZFZvsutg==", "path": "microsoft.aspnetcore.connections.abstractions/9.0.6", "hashPath": "microsoft.aspnetcore.connections.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-fzJfVBPaeLliimP9lyzhE5el7a2eosCLk0saQlkjJH1368Q6belE2nI+thpZ/JZ9koEm0IQPP+6/EwjK/v0I6w==", "path": "microsoft.aspnetcore.http.connections.client/9.0.6", "hashPath": "microsoft.aspnetcore.http.connections.client.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BPXsi0m4L31mZy9HHDrDN5gGjvJkRKktB7ZjHIuX6Cha7WYadGYHGaz9LmUcswCGb00MEJX1aSc0wR//wr56cQ==", "path": "microsoft.aspnetcore.http.connections.common/9.0.6", "hashPath": "microsoft.aspnetcore.http.connections.common.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-hkJXzt7XsNeTd3mUDD1Dhxh5IGd62S1UfaCxvdtIABQy9suY+YoW8KtvK43wUuifuhhqMmpYE0NRAagRy9Kagw==", "path": "microsoft.aspnetcore.jsonpatch/9.0.6", "hashPath": "microsoft.aspnetcore.jsonpatch.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-WjwM/XJuAv0F+kh0ApeWmoxI+9kkAF9nCa6f3GX9rMkN/G2NRPbo2a9uSQy0u8rmaeFXo07P2FoSKQcPM+5n1Q==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/9.0.6", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Owin/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-65jLFRcvMR3n/XK9y9hMYqp9YygMKy7RALj/2KIgz84mYCzlCnKjPeE2JULKQSRDcucrNKfat6tXzIi7unuD0Q==", "path": "microsoft.aspnetcore.owin/9.0.6", "hashPath": "microsoft.aspnetcore.owin.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VX8yakZUQT1+vtTIKQxuFijzS9ET5vmWpSv7uvxb0AoVIb+8KL99FsbJBG7gq4TlCsss9DPEoat42t40yTzM2A==", "path": "microsoft.aspnetcore.signalr.client/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.client.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-sXCPxeECWqVIxfzH4nlV0m17R/IelbJ8B+W1e+E3Ne/wqVUphd+b3MbaSGficNiahRnX1UwKUxT4dAFFilJtnA==", "path": "microsoft.aspnetcore.signalr.client.core/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.client.core.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-sZDm6Nru8wBMOb6Bq9qlTiK5H/8yL7FY08bXydOncJSpVfbj2TwmqjUCR4AmJY6Dvxdhf8f29VgikBSz2EYoiw==", "path": "microsoft.aspnetcore.signalr.common/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.common.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pRCQi9Nu60gyOvGzPXJyP5faSvE4FbDFevnv7/deviodbHULDyqJ82aYhCV98f8AyUh0Yhu98EovIyC9YkdFYg==", "path": "microsoft.aspnetcore.signalr.protocols.json/9.0.6", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.9.0.6.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-owmu2Cr3IQ8yQiBleBHlGk8dSQ12oaF2e7TpzwJKEl4m84kkZJjEY1n33L67Y3zM5jPOjmmbdHjbfiL0RqcMRQ==", "path": "microsoft.bcl.asyncinterfaces/9.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-DC5I4Y1nK35jY4piDqQCzWjDXzT6ECMctBAxgAJoc6pn0k6uyxcDeOuVDRooFui/N65ptn9xT5mk9eO4mSTj/g==", "path": "microsoft.extensions.configuration.commandline/9.0.6", "hashPath": "microsoft.extensions.configuration.commandline.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RGYG2JBak9lf2rIPiZUVmWjUqoxaHPy3XPhPsJyIQ8QqK47rKvJz7jxVYefTnYdM5LTEiGFBdC7v3+SiosvmkQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.6", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "path": "microsoft.extensions.configuration.json/9.0.6", "hashPath": "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0ZZMzdvNwIS0f09S0IcaEbKFm+Xc41vRROsA/soeKEpzRISTDdiVwGlzdldbXEsuPjNVvNHyvIP8YW2hfIig0w==", "path": "microsoft.extensions.configuration.usersecrets/9.0.6", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mIqCzZseDK9SqTRy4LxtjLwjlUu6aH5UdA6j0vgVER14yki9oRqLF+SmBiF6OlwsBSeL6dMQ8dmq02JMeE2puQ==", "path": "microsoft.extensions.diagnostics/9.0.6", "hashPath": "microsoft.extensions.diagnostics.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.6", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Features/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q3VizRxWXP185iqK4bFFsADAuLNaJX6IUsc3lE8/efc5xp1Wu7iYPgJPFUj2Ur4X0d7gfeHkprtY72ThmwA05Q==", "path": "microsoft.extensions.features/9.0.6", "hashPath": "microsoft.extensions.features.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-1HJ<PERSON>bwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Iu1UyXUnjMhoOwThKM0kCyjgWqqQnuujsbPMnF44ITUbmETT7RAVlozNgev2L/damwNoPZKpmwArRKBy2IOAZg==", "path": "microsoft.extensions.hosting/9.0.6", "hashPath": "microsoft.extensions.hosting.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-G9T95JbcG/wQpeVIzg0IMwxI+uTywDmbxWUWN2P0mdna35rmuTqgTrZ4SU5rcfUT3EJfbI9N4K8UyCAAc6QK8Q==", "path": "microsoft.extensions.hosting.abstractions/9.0.6", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Up8Juy8Bh+vL+fXmMWsoSg/G6rszmLFiF44aI2tpOMJE7Ln4D9s37YxOOm81am4Z+V7g8Am3AgVwHYJzi+cL/g==", "path": "microsoft.extensions.localization/9.0.0", "hashPath": "microsoft.extensions.localization.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wc7PaRhPOnio5Csj80b3UgBWA5l6bp28EhGem7gtfpVopcwbkfPb2Sk8Cu6eBnIW3ZNf1YUgYJzwtjzZEM8+iw==", "path": "microsoft.extensions.localization.abstractions/9.0.0", "hashPath": "microsoft.extensions.localization.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "path": "microsoft.extensions.logging.configuration/9.0.6", "hashPath": "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-L1O0M3MrqGlkrPYMLzcCphQpCG0lSHfTSPrm1otALNBzTPiO8rxxkjhBIIa2onKv92UP30Y4QaiigVMTx8YcxQ==", "path": "microsoft.extensions.logging.console/9.0.6", "hashPath": "microsoft.extensions.logging.console.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-u21euQdOjaEwmlnnB1Zd4XGqOmWI8FkoGeUleV7n4BZ8HPQC/jrYzX/B5Cz3uI/FXjd//W88clPfkGIbSif7Jw==", "path": "microsoft.extensions.logging.debug/9.0.6", "hashPath": "microsoft.extensions.logging.debug.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IyyGy7xNJAjdlFYXc7SZ7kS3CWd3Ma4hing9QGtzXi+LXm8RWCEXdKA1cPx5AeFmdg3rVG+ADGIn44K14O+vFA==", "path": "microsoft.extensions.logging.eventlog/9.0.6", "hashPath": "microsoft.extensions.logging.eventlog.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ayCRr/8ON3aINH81ak9l3vLAF/0pV/xrfChCbIlT2YnHAd4TYBWLcWhzbJWwPFV4XmJFrx/z8oq+gZzIc/74OA==", "path": "microsoft.extensions.logging.eventsource/9.0.6", "hashPath": "microsoft.extensions.logging.eventsource.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "path": "microsoft.extensions.objectpool/5.0.10", "hashPath": "microsoft.extensions.objectpool.5.0.10.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lEfViJE4XxrIl4DiNn+Hs9ab/A5nN8eDjV3ZVloya1JsXfaKZQepg9/zMjIJXH1MUE7TrzqXdQfZnp3cwr/rSA==", "path": "microsoft.win32.registry.accesscontrol/9.0.6", "hashPath": "microsoft.win32.registry.accesscontrol.9.0.6.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mUa3Chovao0xQ74j2hUN5kDTn3luY/1e9usJYT0r51cgqfQsgQu5C8Cis9h65SNRbkbBwErfXKlBtHCkMiYa/g==", "path": "microsoft.win32.systemevents/9.0.6", "hashPath": "microsoft.win32.systemevents.9.0.6.nupkg.sha512"}, "Microsoft.Windows.Compatibility/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nzETMdHB75VmEfifhWnv7je2ma9YLoMjzpIKI5JuB2jTdOlUoQFMYnroASg18w/oQEpqY0VA6I6tr/yzuWKwCQ==", "path": "microsoft.windows.compatibility/9.0.6", "hashPath": "microsoft.windows.compatibility.9.0.6.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-S1Gu7KIiBM5Zfkve5iaVZUMPToGZ8fc13IMLNdyU20G9nq9LnKSN5e0xb/TFr4N6IqWkAxmTD4JkcWXdjdk33g==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-B5GL3MQcQ4OI02Q3jXdiISS5N7ZI6LCHyDQTpfJTpzTdf4SDwTMMxrcpGaPSth6l7yVyAtJJbIhgdFDw3PmOhg==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7Aq/6BqdFCShOlL+f7ltFCQkwo/YFzZ+wOmK8ObpGfzhxWp2Mg7H4DuMoqd1pO+ikdfbOcDm7cfdMmsUwgbKkQ==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LG0iGAvkdSWD3WT+oZcozJFZKeINQ160n68YfnwVgtmFpMR2O3GIfuMrf9WJjfnZJb6pbaNnLGqOTpXVJTJa2Q==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RzAA2D77LRuYVTUXKG8wLRbbDF6BA+fHeBtsdar3ARj3cWmqscR3sb5QgROBKtDj1G2Idu3aj+5Bk3TYc+f4XA==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a9pVVDsO76cdoGN3CYEgxTE54C5udq9hfOjPKVxyfwOk1N12w18VKL2a15deezFMqjGggaVyK0cFBj9qG7pqWw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Kb8CZNgH0aWd8Ks6jS1F286SmVJ4FmJjiUdrQTvHu1aN1cWpfwLZ1qOARvFI3lbXE/geOzBIHDNWmQjyOAeUlg==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BCbWsWzSdv9ly9pR1JFY89an+8nc9yZtjAPPlcpH31UUP0AuI27rnrKzcZAuqFXlKy2M8EZVnjV0czSTFueqGA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-eapJ2k/eeMK7Q/Mbticy5rgSb+qI884W9Wk6UTDnEdRpd0BvKbhgM845QEmk3vrxT6B8cCr4A8pRseZBdmk4WA==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-+3S2ksd6iFNeyAxtCZ2dGlxXNGQsOgIgGiecu34++UnUTY9KFhkg8T69hyjEMg4+dRQXEWrU4+vP4AI3s5GlMw==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Fa+EbKuQ6W4EzdVRAA/6ffJ3C0eQ93D8bhmnFaVEHBkfDTKNUSZKhjLdYgubvMrSQlsQ8XLGw0Ld1UXMgGCj7w==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-POgI6/WVHtpxbqvfFqVybZckRbgFVp3nE0fOBpIQdKiZ9C3MPKKibyFNEBK81ZlgmtTEpJP0jMvLSuEbA/p95g==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VMJ5KlYwc2OUPEXmuMetGdwU2IKCDH0mYPz+7G0e2psKJ6Q4JVq9VIOK/nnFJ9z0nbw7Cxu5m7nwh8p/ZPr/eA==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-kbPhYFOoSo7l71lZo4RFx51Vj6BbQAL3QFn4duULbrpV1GEQX5ZrmBSpdxigcvDMit1i/+wDTyMll9t56i/knQ==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Q8gHRVpOlTQSEqKtimf7s3lr8OaYaxXcrXjtF78k+6RogQ0BpEHnUgeBZBoQ53qSiztBAzkF22uPOHq+/+goOA==", "path": "runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-a+KcHUMoNJG1Z9uvf28HDmT8jK5rm0ExTUdScsdP/ukU2KE7ah+vLxNbh4zCxzvGHsx+Z6bVpaWLjuSYNbqilQ==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ogySJ/MVq9J/X1ugQOAA4dQfoJSUnZtIbPLr8t2tsaGkV7TBgWOnFInRXy1c20o79M6ARyus12UinDKsFaLkwA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.6", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.6.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.AspNetCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "path": "serilog.aspnetcore/9.0.0", "hashPath": "serilog.aspnetcore.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "path": "serilog.formatting.compact/3.0.0", "hashPath": "serilog.formatting.compact.3.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Async/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-SnmRknWsSMgyo9wDXeZZCqSp48kkQYy44taSM6vcpxfiRICzSf09oLKEmVr0RCwQnfd8mJQ2WNN6nvhqf0RowQ==", "path": "serilog.sinks.async/2.1.0", "hashPath": "serilog.sinks.async.2.1.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-0Mk8/31efsxFNVeFzLjUVYDFTjYKtEi2UCSaI9+HIkSQ/duu++28gfq1+gwWDRLHkRMOkUgoWMLe0UoayAZySA==", "path": "swashbuckle.aspnetcore/6.8.0", "hashPath": "swashbuckle.aspnetcore.6.8.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-f6Utk5eLFWRJbDFIqHGeb9Kb+w/9b+z0WIJUIAPY4hP263MPXI8ZbejdOktS10gvCLaQ3YCFl6fAVlCIUFKsRQ==", "path": "swashbuckle.aspnetcore.swagger/6.8.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.8.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-bmWglb1jeKD+tUE56wL2zE6lnlpJZbgDLXMlEOgl8xTqEs9g8C6eNNKGuMcnfxFt0CgZaBClaDqad2JDjZW5Fw==", "path": "swashbuckle.aspnetcore.swaggergen/6.8.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.8.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-tG1FL+aFmmuqZ3eKVHgvSFvrzJoKbDzOmvK8YACucfbIBYfHQd45uQ18MhMF3xDaR46qdggdaHGqmfYo+nxwrw==", "path": "swashbuckle.aspnetcore.swaggerui/6.8.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.8.0.nupkg.sha512"}, "System.CodeDom/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-9u1pyEykc0bWBHf1cIVwRoMqrEtxtXdC2ss1K02pvrkwHPcyYmy1glO+kLbyqPO9ehCTl+2dFyUuTUNl1Fde5g==", "path": "system.codedom/9.0.6", "hashPath": "system.codedom.9.0.6.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhkXUl2gNrQtvPmtBTQHb0YsUrDiDQ2QS09YbtTTiSjGcf7NBqtYbrG/BE06zcBPCKEwQGzIv13IVdXNOSub2w==", "path": "system.collections.immutable/9.0.0", "hashPath": "system.collections.immutable.9.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.ComponentModel.Composition/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWAMVgDbk/iD8KRV+aFBgE2EmDbDK3xYeHaDTs68PXRKdjx/s/0q03YCw6oZGVnY/heBs07KUnCYaZbUsnmBCA==", "path": "system.componentmodel.composition/9.0.6", "hashPath": "system.componentmodel.composition.9.0.6.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Esfx8XiOJNy3YkIRIEcPd5DIDORkvD+NyE3gqzwyNdY9Wz8V6y9boMOzKzb4mGfn4ZqqyRdvJluh8WxKbm2Tpw==", "path": "system.componentmodel.composition.registration/9.0.6", "hashPath": "system.componentmodel.composition.registration.9.0.6.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GQYhl3XCSGhxQvbRCjQiuGbJjm1tlq2lu98SGItjeingM7D+uKNnmKlK0MMuAU1asT7YtJ8uoT83WnGCuna9Qg==", "path": "system.configuration.configurationmanager/9.0.6", "hashPath": "system.configuration.configurationmanager.9.0.6.nupkg.sha512"}, "System.Data.Odbc/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IvXvGdpQUs/+GAaBcOTiKyJ+08/Da54HpN+2JcxgKVdL0v3RW2YvSZR5H4IHgUUuRk/16vwohnLQc9tnNbzw8g==", "path": "system.data.odbc/9.0.6", "hashPath": "system.data.odbc.9.0.6.nupkg.sha512"}, "System.Data.OleDb/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-hUIdIhMPpcuo9V47iYjvQAoW/iwIZHj2AJmKos2MW/4ZGjQhpSxXfjJUprYOAyzsvFO0EE8oP46D0vNmawREPg==", "path": "system.data.oledb/9.0.6", "hashPath": "system.data.oledb.9.0.6.nupkg.sha512"}, "System.Data.SqlClient/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "path": "system.data.sqlclient/4.9.0", "hashPath": "system.data.sqlclient.4.9.0.nupkg.sha512"}, "System.Data.SQLite/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-JSOJpnBf9goMnxGTJFGCmm6AffxgtpuXNXV5YvWO8UNC2zwd12qkUe5lAbnY+2ohIkIukgIjbvR1RA/sWILv3w==", "path": "system.data.sqlite/1.0.119", "hashPath": "system.data.sqlite.1.0.119.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-BwwgCSeA80gsxdXtU7IQEBrN9kQXWQrD11hNYOJZbXBBI1C4r7hA4QhBAalO1nzijXikthGRUADIEMI3nlucLA==", "path": "system.data.sqlite.ef6/1.0.119", "hashPath": "system.data.sqlite.ef6.1.0.119.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lum+Dv+8S4gqN5H1C576UcQe0M2buoRjEUVs4TctXRSWjBH3ay3w2KyQrOo1yPdRs1I+xK69STz+4mjIisFI5w==", "path": "system.diagnostics.eventlog/9.0.6", "hashPath": "system.diagnostics.eventlog.9.0.6.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-CrOJB8Iw+XsgD5Tt+PETBdZzWpLEw0FgobKESfcyrn4kxcg9FNz3r1cP5n1c35cTrZNbYCbOnk/xB/0AL1UtLw==", "path": "system.diagnostics.performancecounter/9.0.6", "hashPath": "system.diagnostics.performancecounter.9.0.6.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-jb9CXtAkrgs7xnp7cv4JEWoOvhIIO1BDub7pBdg456RoFiIVFBaj46twJ6kOsq0QKJKxj4P2GMyBERLHr6OPPA==", "path": "system.directoryservices/9.0.6", "hashPath": "system.directoryservices.9.0.6.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xxbRffj3vJLrG39HDPk10V2Ia805bIsof8a7I4guX/1kN+diC849HGS9FePMukzNjBcJWihtCZHf6bJiJnYrtw==", "path": "system.directoryservices.accountmanagement/9.0.6", "hashPath": "system.directoryservices.accountmanagement.9.0.6.nupkg.sha512"}, "System.DirectoryServices.Protocols/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-6xlvg/xYleDEs/mG+lRDFqRs9pvep2Ejd2bSf+NdudLTETL0ggKKDxYeYrEQavMO70kryvTmA7fHuLG8Q0hFYw==", "path": "system.directoryservices.protocols/9.0.6", "hashPath": "system.directoryservices.protocols.9.0.6.nupkg.sha512"}, "System.Drawing.Common/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-KpPB6e7PYCalslhmq/dVa2tJgCcfUDhEK83j1Eix+BmEcNPP00JJBvFrBT7jGcHLmQzsIA4AthwNFkk+cD3RKA==", "path": "system.drawing.common/9.0.6", "hashPath": "system.drawing.common.9.0.6.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-WPpjTjDayc21MzH4E6reg3wJZ2L14ZbStT5mhG1eBR0V0bqvTkjAMj9akjx4go52mFPEcy74ZjXMF/1S8lXjnQ==", "path": "system.io.packaging/9.0.6", "hashPath": "system.io.packaging.9.0.6.nupkg.sha512"}, "System.IO.Ports/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-D1nZmsZfUfKQ9/AqiAEafmnYijUoJvXtl0RWZ2P+q/Wq3gXgEtp+NzKTpabw2s0aiuPAsdx8SujQY06W2X4ucQ==", "path": "system.io.ports/9.0.6", "hashPath": "system.io.ports.9.0.6.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-DArhXxeawjiGbMsYdc0oUvZIjDzcLqnwz+Us8A+wJhrOiz3EqCO8aFoYun2GokyaW2hipEJ33oxqnhU6UmaEJQ==", "path": "system.linq.dynamic.core/1.4.5", "hashPath": "system.linq.dynamic.core.1.4.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Management/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pOc5bCurWL3qVDsPP0iycUCRfXBhI/fVe44SiAlVqoZDIbHP080CLyCzfXV1UbdGKN0hQSLSHqr7OI3BhLBRbA==", "path": "system.management/9.0.6", "hashPath": "system.management.9.0.6.nupkg.sha512"}, "System.Net.ServerSentEvents/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-gLRGZulz4D11qb9tGKlgxdSIQ8kB//x7u1nPJ8NaDyf1MZM+nLbzefXOWR9MUR+yGT/eI2iYTyj6pxwvQ5AzeQ==", "path": "system.net.serversentevents/9.0.6", "hashPath": "system.net.serversentevents.9.0.6.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-BcUV7OERlLqGxDXZuIyIMMmk1PbqBblLRbAoigmzIUx/M8A+8epvyPyXRpbgoucKH7QmfYdQIev04Phx2Co08A==", "path": "system.private.servicemodel/4.10.3", "hashPath": "system.private.servicemodel.4.10.3.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Context/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-f9DgmWmXiSQC4CmJudIPO1zjVgC9MEHHbTPY00pJ/2nOqAagr6PXeXkSTb9XEh8y/sWnkL1GbMpYGrUoh2kosA==", "path": "system.reflection.context/9.0.6", "hashPath": "system.reflection.context.9.0.6.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3U3JgtbP8+1+yDsGC4f5wFMi2S1ERaA5fsy0CPph0hdeMDaWDvGnUvH5VDkQZ1bCtV9UZN4hlYMxtMQ4TjluaQ==", "path": "system.runtime.caching/9.0.6", "hashPath": "system.runtime.caching.9.0.6.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Gny8p2mX0jc5rjh+PA4Gx5GG66sj2C+e+ro7+j/3IsKT/bmQ84tGRV+XKaG+5/CTCdwkSSKDWEQ1rJd0J5jE0Q==", "path": "system.security.cryptography.pkcs/9.0.6", "hashPath": "system.security.cryptography.pkcs.9.0.6.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-yErfw/3pZkJE/VKza/Cm5idTpIKOy/vsmVi59Ta5SruPVtubzxb8CtnE8tyUpzs5pr0Y28GUFfSVzAhCLN3F/Q==", "path": "system.security.cryptography.protecteddata/9.0.6", "hashPath": "system.security.cryptography.protecteddata.9.0.6.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uwlfOnvJd7rXRvP3aV126Q9XebIIEGEaZ245Rd5/ZwOg7U7AU+AmpE0vRh2F0DFjfOTuk7MAexv4nYiNP/RYnQ==", "path": "system.security.cryptography.x509certificates/4.3.2", "hashPath": "system.security.cryptography.x509certificates.4.3.2.nupkg.sha512"}, "System.Security.Cryptography.Xml/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-8x/rARnaTCf41lpnSBV06SjIZT/nAFqingQr835wxmaGvdGuNKR8s/+YtG20F2f++add/eQSr1S3ww/iYzOkrA==", "path": "system.security.cryptography.xml/9.0.6", "hashPath": "system.security.cryptography.xml.9.0.6.nupkg.sha512"}, "System.Security.Permissions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ldz76otOJi3B+6k2nZ4m0t/76WvvO9AjlkWGP3wQf8CnzoqMm3O2KjEstahIH6m2z1rWlB6m05YzPTS08FXiRA==", "path": "system.security.permissions/9.0.6", "hashPath": "system.security.permissions.9.0.6.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZ8ZahvTenWML7/jGUXSCm6jHlxpMbcb+Hy+h5p1WP9YVtb+Er7FHRRGizqQMINEdK6HhWpD6rzr5PdxNyusdg==", "path": "system.servicemodel.duplex/4.10.3", "hashPath": "system.servicemodel.duplex.4.10.3.nupkg.sha512"}, "System.ServiceModel.Http/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-hodkn0rPTYmoZ9EIPwcleUrOi1gZBPvU0uFvzmJbyxl1lIpVM5GxTrs/pCETStjOXCiXhBDoZQYajquOEfeW/w==", "path": "system.servicemodel.http/4.10.3", "hashPath": "system.servicemodel.http.4.10.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-tP7GN7ehqSIQEz7yOJEtY8ziTpfavf2IQMPKa7r9KGQ75+uEW6/wSlWez7oKQwGYuAHbcGhpJvdG6WoVMKYgkw==", "path": "system.servicemodel.nettcp/4.10.3", "hashPath": "system.servicemodel.nettcp.4.10.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-aNcdry95wIP1J+/HcLQM/f/AA73LnBQDNc2uCoZ+c1//KpVRp8nMZv5ApMwK+eDNVdCK8G0NLInF+xG3mfQL+g==", "path": "system.servicemodel.primitives/4.10.3", "hashPath": "system.servicemodel.primitives.4.10.3.nupkg.sha512"}, "System.ServiceModel.Security/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-vqelKb7DvP2inb6LDJ5Igi8wpOYdtLXn5luDW5qEaqkV2sYO1pKlVYBpr6g6m5SevzbdZlVNu67dQiD/H6EdGQ==", "path": "system.servicemodel.security/4.10.3", "hashPath": "system.servicemodel.security.4.10.3.nupkg.sha512"}, "System.ServiceModel.Syndication/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-x1fabx4YAzfUFMTUZvNYk2I61yw3+9Mfl+Qkue9HAnMnrlV84srcuzTAcagr3a+OaAWGQLUNv1OIFF/L8ZLw+g==", "path": "system.servicemodel.syndication/9.0.6", "hashPath": "system.servicemodel.syndication.9.0.6.nupkg.sha512"}, "System.ServiceProcess.ServiceController/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ba3cPFu9mNtSJ5DTYsDWIkAheBIOENxnsDsrvtk7TbfgcaYMP9oV5+kPH0c/ENkuD1TSpBT5U2tC1Pe7OiDWLA==", "path": "system.serviceprocess.servicecontroller/9.0.6", "hashPath": "system.serviceprocess.servicecontroller.9.0.6.nupkg.sha512"}, "System.Speech/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Cyc9GLB8OpPNFFSlvnaLzkLSyGCuIENllB47w0WwP+SzVfDWvGdTEK1iIaMQEoRjpdl4sRYrchCDYx6GqNlu/w==", "path": "system.speech/9.0.6", "hashPath": "system.speech.9.0.6.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0VlUQoPaGOpiFppKjvhAiMZ5WngPGcbL3EQlxDSe60WcleFKNHVdqiwRDSJmjA7xtMURWfcvfjb2AtFBkwqZEQ==", "path": "system.text.encoding.codepages/9.0.6", "hashPath": "system.text.encoding.codepages.9.0.6.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.AccessControl/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-cwFASP4ODq8qdzWm+HkzFtzu0c6AtMLEuhctck1HMOwUdQNwL07IEZxOJaNtsg2urOaa7SN6EbDDUZOgATbjNA==", "path": "system.threading.accesscontrol/9.0.6", "hashPath": "system.threading.accesscontrol.9.0.6.nupkg.sha512"}, "System.Threading.Channels/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2MaNJVkG2yJiXQbZrrcYoJ55ehV+aX0zqR6rWJkO/Qj7jTsArWthrQ7iWywUf/sE5ylJWX/iLH2kKfwSRdkWsA==", "path": "system.threading.channels/9.0.6", "hashPath": "system.threading.channels.9.0.6.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Web.Services.Description/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-ORCkTkUo9f1o4ACG+H6SV+0XSxVZ461w3cHzYxEU41y6aKWp1CeNTMYbtdxMw1we6c6t4Hqq15PdcLVcdqno/g==", "path": "system.web.services.description/4.10.3", "hashPath": "system.web.services.description.4.10.3.nupkg.sha512"}, "System.Windows.Extensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-t0NLE1cAXUmO2aiwCjesf9TVwttjaHnkKpiFBTm/d10QnQD6aiGYmrWLhS0HBIo4utB/jPkgRF0SJVbgrgfZ/Q==", "path": "system.windows.extensions/9.0.6", "hashPath": "system.windows.extensions.9.0.6.nupkg.sha512"}, "Volo.Abp.Autofac/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-DlCMfwI8uG48IRlbwS1iNYT2WAt8TosFwDfMfWbXtTVRxxNMwIl9ISq/+pKIKZo6KOeui/cxEidAq3lXCtoDyA==", "path": "volo.abp.autofac/9.0.6", "hashPath": "volo.abp.autofac.9.0.6.nupkg.sha512"}, "Volo.Abp.Castle.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-QW7zsGEevy0ekh+qDbKR5UU0mX6x5Xd51CH68894ym4gaPr2LFGsYL4dAHsf6BR3+naKb4wtWfZXPMTOEqe52w==", "path": "volo.abp.castle.core/9.0.6", "hashPath": "volo.abp.castle.core.9.0.6.nupkg.sha512"}, "Volo.Abp.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Tt03rOozvLiRW1u+7AnUwKJKueAipN5DRArFGVBcAZ2LbZ+9dT6xAG2vVJZEapCnXGHE0zdJEajp/f5XooTfYA==", "path": "volo.abp.core/9.0.6", "hashPath": "volo.abp.core.9.0.6.nupkg.sha512"}, "Mis.Shared.Interface/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}