﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Mis.Shared.Interface;
using Mis.Agent.Barcode.Resources;

namespace Mis.Agent.Barcode
{
    public partial class ScannerForm : Form, ICultureChangeNotifiable
    {
        private readonly IScannerAppService _scannerAppService;
        public ScannerForm(IScannerAppService scannerService)
        {
            _scannerAppService = scannerService;
            InitializeComponent();
            InitializeLocalization();
            LoadScannerConfigurations();
            //_scannerAppService.scannerImageCaptured += OnScannerImageCaptured; // Subscribe to the event
        }

        private void InitializeLocalization()
        {
            // Register for culture change notifications
            NotificationManager.RegisterForCultureChange(this);

            // Apply initial localization
            ApplyLocalization();
        }

        public void OnCultureChanged()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged()));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = BarcodeResources.ScannerFormTitle;

            // Update tab text
            ScannerTab.Text = BarcodeResources.ScannerTabText;

            // Update labels
            label7.Text = BarcodeResources.AvailableScannersLabel;

            // Update checkboxes
            checkBoxUseBarcodeReader.Text = BarcodeResources.UseBarcodeReaderCheckbox;

            // Update buttons
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Unregister from culture change notifications
            NotificationManager.UnregisterFromCultureChange(this);
            base.OnFormClosing(e);
        }




        private void LoadScannerConfigurations()
        {
            // Retrieve the value of IsScanByBarcodeReader from the configuration (appsettings.json)
            bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));

            // Set checkbox state based on configuration
            checkBoxUseBarcodeReader.Checked = isScanByBarcodeReader;

            // Populate available scanners
            List<string> availableScanners = _scannerAppService.GetAvailableScanners();

            if (availableScanners != null && availableScanners.Count > 0)
            {
                comboBoxScanners.DataSource = availableScanners;

                // Retrieve the selected scanner from appsettings
                string selectedScanner = _scannerAppService.GetSetting("Scanner");

                // Set the selected scanner in the ComboBox if it exists in the available scanners
                if (availableScanners.Contains(selectedScanner))
                {
                    comboBoxScanners.SelectedItem = selectedScanner;
                }
            }
            else
            {
                MessageBox.Show("No available scanners found.");
            }

            // Enable or disable the ComboBox based on the value of isScanByBarcodeReader
            comboBoxScanners.Enabled = !isScanByBarcodeReader;
            pictureScanned.SizeMode = PictureBoxSizeMode.StretchImage; // To stretch the image
            //pictureScanned.Dock = DockStyle.Fill;
        }

        //private void SaveScannerConfigurations_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        string selectedScanner = comboBoxScanners.SelectedItem?.ToString();
        //        bool isScanByBarcodeReader = checkBoxUseBarcodeReader.Checked;

        //        if (string.IsNullOrEmpty(selectedScanner) && !isScanByBarcodeReader)
        //        {
        //            MessageBox.Show("Please select a scanner.");
        //            return;
        //        }

        //        // Save the configuration
        //        _scannerAppService.SaveScannerConfiguration(selectedScanner, isScanByBarcodeReader);



        //        _scannerAppService.ShowNotification("Settings Saved", "Scanner configuration saved successfully.");

        //    }
        //    catch (Exception ex)
        //    {
        //        _scannerAppService.ShowNotification("Error", $"Failed to save default scanner settings: {ex.Message}");
        //    }
        //}

        private void checkBoxUseBarcodeReader_CheckedChanged(object sender, EventArgs e)
        {
            comboBoxScanners.Enabled = !checkBoxUseBarcodeReader.Checked;

        }

    }
}
