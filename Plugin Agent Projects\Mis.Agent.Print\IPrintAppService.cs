﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Print
{
    public interface IPrintAppService
    {

        bool PrintAsync(TransactionDto transactionDto);


        PaperSize[] GetPaperSizes(string printerName);
        IEnumerable<string> GetInstalledPrinters();
        void SetPaperSize(PaperSize paperSize);
        void SetPrinter(string printerName);
        bool PrintPdf(string pdfFilePath, string printerName, PaperSize paperSize);




        void ShowNotification(string title, string text);
        PrinterEntity GetPrinterEntity();
    }
}
