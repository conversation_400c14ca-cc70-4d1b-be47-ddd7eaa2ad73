﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BarcodeForm" xml:space="preserve">
    <value>واجهة الباركود</value>
  </data>
  <data name="BarcodeTab.Text" xml:space="preserve">
    <value>الباركود</value>
  </data>
  <data name="PortTabText" xml:space="preserve">
    <value>إعدادات المنفذ</value>
  </data>
  <data name="AgentUrlLabel" xml:space="preserve">
    <value>عنوان التطبيق :</value>
  </data>
  <data name="SavePortConfigButton" xml:space="preserve">
    <value>حفظ إعدادات التطبيق</value>
  </data>
  <data name="BtnSaveAllSettings.Text" xml:space="preserve">
    <value>حفظ الإعدادات</value>
  </data>
  <data name="BtnSwitchLanguage.Text" xml:space="preserve">
    <value>تبديل اللغة</value>
  </data>
  <data name="MainFormTitle" xml:space="preserve">
    <value>نموذج الوكيل</value>
  </data>
  <data name="PrintTab" xml:space="preserve">
    <value>الطباعة</value>
  </data>
  <data name="TabScanner" xml:space="preserve">
    <value>الماسح</value>
  </data>
  <data name="NotificationsTab" xml:space="preserve">
    <value> إعدادات الإشعارات</value>
  </data>
  <data name="PortTab" xml:space="preserve">
    <value>المنفذ</value>
  </data>
  <data name="SwitchToArabic" xml:space="preserve">
    <value>التبديل إلى العربية</value>
  </data>
  <data name="SwitchToEnglish" xml:space="preserve">
    <value>التبديل إلى الإنجليزية</value>
  </data>
  <data name="LanguageSwitched" xml:space="preserve">
    <value>تم تبديل اللغة</value>
  </data>
  <data name="LanguageSwitchedMessage" xml:space="preserve">
    <value>تم تبديل اللغة بنجاح.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="ErrorMessage" xml:space="preserve">
    <value>حدث خطأ</value>
  </data>
  <data name="ConnectionSuccess" xml:space="preserve">
    <value>نجح الاتصال</value>
  </data>
  <data name="ConnectionSuccessMessage" xml:space="preserve">
    <value>تم إنشاء الاتصال بنجاح</value>
  </data>
  <data name="ConnectionFailed" xml:space="preserve">
    <value>فشل الاتصال</value>
  </data>
  <data name="ConnectionFailedMessage" xml:space="preserve">
    <value>فشل في إنشاء الاتصال</value>
  </data>
  <data name="SettingsSaved" xml:space="preserve">
    <value>تم حفظ الإعدادات</value>
  </data>
  <data name="SettingsSavedMessage" xml:space="preserve">
    <value>تم حفظ الإعدادات بنجاح</value>
  </data>
  <data name="BarcodeScanned" xml:space="preserve">
    <value>تم مسح الباركود</value>
  </data>
  <data name="BarcodeScannedMessage" xml:space="preserve">
    <value>تم مسح الباركود بنجاح</value>
  </data>
  <data name="PrintJobCompleted" xml:space="preserve">
    <value>اكتملت مهمة الطباعة</value>
  </data>
  <data name="PrintJobCompletedMessage" xml:space="preserve">
    <value>تم إكمال مهمة الطباعة بنجاح</value>
  </data>
  <data name="PortConnected" xml:space="preserve">
    <value>تم توصيل المنفذ</value>
  </data>
  <data name="PortConnectedMessage" xml:space="preserve">
    <value>تم إنشاء اتصال المنفذ بنجاح</value>
  </data>
  <data name="PortDisconnected" xml:space="preserve">
    <value>تم قطع اتصال المنفذ</value>
  </data>
  <data name="PortDisconnectedMessage" xml:space="preserve">
    <value>تم قطع اتصال المنفذ</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>نجح</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>معلومات</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>تأكيد</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>نعم</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>لا</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>موافق</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>
  <data name="BarcodeUrl" xml:space="preserve">
    <value>رابط الباركود</value>
  </data>
  <data name="ComPort" xml:space="preserve">
    <value>منفذ COM</value>
  </data>
  <data name="AvailablePorts" xml:space="preserve">
    <value>المنافذ المتاحة</value>
  </data>
  <data name="SaveBarcodeConfiguration" xml:space="preserve">
    <value>حفظ إعدادات الباركود</value>
  </data>
  <data name="TestConnection" xml:space="preserve">
    <value>اختبار الاتصال</value>
  </data>
  <data name="SendManualData" xml:space="preserve">
    <value>إرسال البيانات يدوياً</value>
  </data>
  <data name="ScannerTab" xml:space="preserve">
    <value>الماسح الضوئي</value>
  </data>
  <data name="AvailableScanners" xml:space="preserve">
    <value>الماسحات المتاحة</value>
  </data>
  <data name="UseBarcodeReader" xml:space="preserve">
    <value>استخدام قارئ الباركود</value>
  </data>
  <data name="SaveScannerConfiguration" xml:space="preserve">
    <value>حفظ إعدادات الماسح</value>
  </data>
  <data name="ScannedImage" xml:space="preserve">
    <value>الصورة الممسوحة</value>
  </data>
  <data name="PrinterName" xml:space="preserve">
    <value>اسم الطابعة</value>
  </data>
  <data name="PaperSize" xml:space="preserve">
    <value>حجم الورق</value>
  </data>
  <data name="DefaultPrinter" xml:space="preserve">
    <value>الطابعة الافتراضية</value>
  </data>
  <data name="CurrentPrinter" xml:space="preserve">
    <value>الطابعة الحالية</value>
  </data>
  <data name="CurrentPaperSize" xml:space="preserve">
    <value>حجم الورق الحالي</value>
  </data>
  <data name="TestPrint" xml:space="preserve">
    <value>طباعة تجريبية</value>
  </data>
  <data name="SavePrintConfiguration" xml:space="preserve">
    <value>حفظ إعدادات الطباعة</value>
  </data>
  <data name="EnableNotifications" xml:space="preserve">
    <value>تفعيل/إلغاء الإشعارات</value>
  </data>
  <data name="ClearNotifications" xml:space="preserve">
    <value>مسح الإشعارات</value>
  </data>
  <data name="NotificationId" xml:space="preserve">
    <value>المعرف</value>
  </data>
  <data name="NotificationNumber" xml:space="preserve">
    <value>الرقم</value>
  </data>
  <data name="IsPrinted" xml:space="preserve">
    <value>مطبوع</value>
  </data>
  <data name="ReceiveTime" xml:space="preserve">
    <value>وقت الاستلام</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>حدث خطأ</value>
  </data>
  <data name="InvalidUrl" xml:space="preserve">
    <value>رابط غير صحيح</value>
  </data>
  <data name="PrinterNotFound" xml:space="preserve">
    <value>لم يتم العثور على الطابعة</value>
  </data>
  <data name="ScannerNotFound" xml:space="preserve">
    <value>لم يتم العثور على الماسح</value>
  </data>
  <data name="ConfigurationSaved" xml:space="preserve">
    <value>تم حفظ الإعدادات بنجاح</value>
  </data>
  <data name="ConfigurationFailed" xml:space="preserve">
    <value>فشل في حفظ الإعدادات</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>خطأ في قاعدة البيانات</value>
  </data>
  <data name="NotificationsSent" xml:space="preserve">
    <value>تم إرسال الإشعارات بنجاح</value>
  </data>
  <data name="NotificationsCleared" xml:space="preserve">
    <value>تم مسح جميع الإشعارات بنجاح</value>
  </data>
  <data name="ClearNotificationsConfirm" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد مسح جميع الإشعارات؟</value>
  </data>
  <data name="ClearLogsConfirm" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد مسح جميع السجلات؟</value>
  </data>
  <data name="LogsCleared" xml:space="preserve">
    <value>تم مسح جميع السجلات بنجاح</value>
  </data>
  <data name="ScannerConnected" xml:space="preserve">
    <value>تم توصيل الماسح بنجاح</value>
  </data>
  <data name="ScannerDisconnected" xml:space="preserve">
    <value>تم قطع اتصال الماسح</value>
  </data>
  <data name="PrintCompleted" xml:space="preserve">
    <value>تمت الطباعة بنجاح</value>
  </data>
  <data name="PrintFailed" xml:space="preserve">
    <value>فشلت الطباعة</value>
  </data>
  <data name="NoDataToPrint" xml:space="preserve">
    <value>لا توجد بيانات للطباعة</value>
  </data>
  <data name="InvalidConfiguration" xml:space="preserve">
    <value>إعدادات غير صحيحة</value>
  </data>
  <data name="SettingsUpdated" xml:space="preserve">
    <value>تم تحديث الإعدادات بنجاح</value>
  </data>
  <data name="LocalizationTestTitle" xml:space="preserve">
    <value>اختبار التعريب</value>
  </data>
  <data name="WelcomeMessage" xml:space="preserve">
    <value>مرحباً بك في اختبار التعريب</value>
  </data>
  <data name="InstructionsMessage" xml:space="preserve">
    <value>انقر على الزر أعلاه للتبديل بين الإنجليزية والعربية. لاحظ كيف يتغير النص وكيف يتكيف التخطيط مع الكتابة من اليمين إلى اليسار.</value>
  </data>
  <data name="FormTitle" xml:space="preserve">
    <value>وكيل نظام المعلومات الإدارية</value>
  </data>
  <data name="BarcodeFormTitle" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="PrintFormTitle" xml:space="preserve">
    <value>إعدادات الطباعة</value>
  </data>
  <data name="NotificationsFormTitle" xml:space="preserve">
    <value>إدارة الإشعارات</value>
  </data>
  <data name="BarcodeTabText" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="PrintTabText" xml:space="preserve">
    <value>إعدادات الطباعة</value>
  </data>
  <data name="NotificationsTabText" xml:space="preserve">
    <value> إعدادات الإشعارات</value>
  </data>
  <data name="BarcodeUrlLabel" xml:space="preserve">
    <value>رابط الباركود:</value>
  </data>
  <data name="ComPortLabel" xml:space="preserve">
    <value>منفذ COM:</value>
  </data>
  <data name="AvailablePortsLabel" xml:space="preserve">
    <value>المنافذ المتاحة:</value>
  </data>
  <data name="DefaultPrinterLabel" xml:space="preserve">
    <value>الطابعة الافتراضية:</value>
  </data>
  <data name="PrinterNameLabel" xml:space="preserve">
    <value>اسم الطابعة:</value>
  </data>
  <data name="PaperType" xml:space="preserve">
    <value>نوع الورق:</value>
  </data>
  <data name="SaveBarcodeConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الباركود</value>
  </data>
  <data name="TestConnectionButton" xml:space="preserve">
    <value>اختبار الاتصال</value>
  </data>
  <data name="SavePrintConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الطباعة</value>
  </data>
  <data name="Properties" xml:space="preserve">
    <value>خصائص</value>
  </data>
  <data name="ClearNotificationsButton" xml:space="preserve">
    <value>مسح جميع الإشعارات</value>
  </data>
  <data name="EnableNotificationsCheckbox" xml:space="preserve">
    <value>تفعيل/إلغاء الإشعارات</value>
  </data>
  <data name="PortError" xml:space="preserve">
    <value>خطأ في إعدادات المنفذ</value>
  </data>
  <data name="PortInUse" xml:space="preserve">
    <value>هذا المنفذ مستخدم لايمكنك استخدامه !</value>
  </data>
  <data name="InvalidPortFormat" xml:space="preserve">
    <value>الرجاء إدخال رقم منفذ صالح بين 1 و 65535.</value>
  </data>
  <data name="BarcodeError" xml:space="preserve">
    <value>خطأ في إعدادات الباركود</value>
  </data>
  <data name="BarcodeUrlMissing" xml:space="preserve">
    <value>الرجاء إدخال عنوان URL صالح للباركود </value>
  </data>
  <data name="BarcodeUrlInvalid" xml:space="preserve">
    <value>عنوان URL للباركود غير صالح </value>
  </data>
  <data name="ComPortMissing" xml:space="preserve">
    <value>الرجاء اختيار منفذ COM </value>
  </data>
  <data name="AgentSuccess" xml:space="preserve">
    <value>نجحت المهمة</value>
  </data>
  <data name="RestartRequired" xml:space="preserve">
    <value>يجب إعادة التشغيل على الإعدادات الجديدة</value>
  </data>
  <data name="AllSettingsSaved" xml:space="preserve">
    <value>تم حفظ التغييرات بنجاح</value>
  </data>
  <data name="HubConnectionFailed" xml:space="preserve">
    <value>تعذر الاتصال بـ Hub. تحقق من عنوان الرابط أو الاتصال.</value>
  </data>
  <data name="BarcodeSuccess" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
  <data name="HubConnectionSuccess" xml:space="preserve">
    <value>تم الاتصال ب HubbConnection بنجاح .</value>
  </data>
  <data name="UpdateNotification" xml:space="preserve">
    <value>إشعار تعديل</value>
  </data>
  <data name="SettingsUpToDate" xml:space="preserve">
    <value>لايوجد تغيير في البيانات </value>
  </data>
  <data name="NoPortSelectedMessage" xml:space="preserve">
    <value>الرجاء وصل الحاسب بجهاز باركود</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>خطأفي تحديث البيانات</value>
  </data>
  <data name="ErrorSavingSettings" xml:space="preserve">
    <value>خطأ في حفظ الإعدادات</value>
  </data>
  <data name="InvalidUrlMessage" xml:space="preserve">
    <value>الرجاء إدخال عنوان باركود صالح</value>
  </data>
  <data name="BarcodeTab" xml:space="preserve">
    <value>إعدادات الباركود</value>
  </data>
</root>