﻿using Mis.Shared.Interface;
using Microsoft.Extensions.Configuration;
using Mis.Agent.Barcode;
using System;
using System.Configuration;
using System.IO;
using System.IO.Ports;
using System.Net.Http;
using System.Windows.Forms;
using Volo.Abp;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.Windows.Forms;
using ComboBox = System.Windows.Forms.ComboBox;
using TextBox = System.Windows.Forms.TextBox;
using System.Management;
using Microsoft.AspNetCore.SignalR.Client;
using System.Text;
using Microsoft.Extensions.FileSystemGlobbing;
using System.Runtime;
using Mis.Agent.Barcode.Resources;
using Microsoft.Extensions.Logging;
using System.Drawing;
using System.Threading;
namespace Mis.Agent.Barcode
{
    [Plugin("Barcode", Version = "1.0.0", Description = "Barcode scanning and processing", Order = 10)]

    public class BarcodeService : IBarcodeAppService
    {

        private TaskCompletionSource<string> _dataCaptureCompletionSource;
        private ManagementEventWatcher _watcher;
        private readonly IConfiguration _configuration;
        private readonly BarcodeForm _barcodeForm;
        private string _comPort;
        private string _barcodeUrl;
        private HubConnection _hubConnection; // Added HubConnection field
        private string _currentComPort;
        bool _notificationsEnabled;
        private readonly Dictionary<string, string> _settings;
        private readonly string _filePath;
        private ILogger<BarcodeService> _logger;

        // Barcode-specific fields moved from SerialPortManager
        private SerialPort _serialPort;
        private bool _isCaptureImageMode;
        public string LastScannedBase64Data { get; set; }

        public BarcodeService(ILogger<BarcodeService> logger)
        {
            _logger = logger;
            _filePath = Path.Combine(AppContext.BaseDirectory, "appsettings.json");
            if (!File.Exists(_filePath))
            {
                MessageBox.Show($"Missing configuration file: {_filePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }
            _configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();


            _barcodeUrl = GetBarcodeBaseUrl();
            _comPort = GetCOMPort();
            NotificationManager.NotificationEvent += OnNotificationReceived;
            GetNotificationEnabledSetting();
            _barcodeForm = new BarcodeForm(this);

        }
        public void GetNotificationEnabledSetting()
        {
            var section = _configuration.GetSection("NotificationSettings");
            _notificationsEnabled = section.GetValue<bool?>("EnabeledNotification") ?? false;
        }


        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }

        [PluginNotificationHandler]
        public void ShowNotification(string title, string text)
        {
            // Use centralized NotificationManager for consistent localization
            NotificationManager.SetNotificationEnabled(_notificationsEnabled);
            NotificationManager.ShowNotification(title, text);
        }

        [PluginTabProvider]
        public object GetTabPage()
        {
            _logger.LogInformation("Building Barcode TabPage UI.");

            PopulateCOMPorts();
            _logger.LogInformation("COM ports populated.");

            SetBarcodeScannerPort();
            _logger.LogInformation("Barcode scanner port set to: {ComPort}", _comPort);

            _barcodeForm.barcodeUrlTextBox.Text = _barcodeUrl;
            _barcodeForm.comPortTextBox.Text = _comPort;
            _barcodeForm.BarcodeTab.Refresh();

            _logger.LogInformation("TabPage returned successfully.");
            return _barcodeForm.BarcodeTab;
        }

        [PluginInitialize]
        public async Task Initialize()
        {
            _logger.LogInformation("Starting Barcode Initialization...");

            if (string.IsNullOrWhiteSpace(_comPort))
            {
                _logger.LogWarning("COM port is not configured.");
                ShowNotification("Barcode Notification", "No COM port available for listening.");
                return;
            }

            if (string.IsNullOrWhiteSpace(_barcodeUrl))
            {
                _logger.LogWarning("Barcode URL is not configured.");
                ShowNotification("Barcode Notification", "No Barcode URL available for listening.");
                return;
            }

            try
            {
                _logger.LogInformation("Attempting to initialize barcode with URL: {BarcodeUrl} and COM Port: {ComPort}", _barcodeUrl, _comPort);
                await PublicInitialize(_barcodeUrl, _comPort, false);
                _logger.LogInformation("Barcode initialized successfully.");
                NotificationManager.ShowNotification("BarcodeSuccess", "BarcodeInitializedMessage", "Barcode Success", "Barcode initialized successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during Barcode Initialization.");
                NotificationManager.ShowNotification("BarcodeError", "BarcodeInitializationFailed", "Barcode Error", "Failed to initialize barcode.");
            }
        }




        public async Task PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            _logger.LogInformation("PublicInitialize called with URL: {BarcodeUrl}, COM Port: {ComPort}, CaptureMode: {CaptureImageMode}",
                barcodeUrl, comPort, isCaptureImageMode);

            try
            {
                if (string.IsNullOrEmpty(comPort))
                {
                    _logger.LogWarning("COM Port is null or empty.");
                    throw new ArgumentException("COM Port cannot be null or empty", nameof(comPort));
                }

                if (string.IsNullOrEmpty(barcodeUrl))
                {
                    _logger.LogWarning("Barcode URL is null or empty.");
                    throw new ArgumentException("Base URL cannot be null or empty", nameof(barcodeUrl));
                }

                SetCaptureImageMode(isCaptureImageMode);
                _logger.LogInformation("Capture image mode set to: {CaptureImageMode}", isCaptureImageMode);

                _logger.LogInformation("Initializing hub connection...");
                bool isConnected = await InitializeHubConnection(barcodeUrl);

                if (isConnected)
                {
                    _logger.LogInformation("Hub connection successful. Starting to listen on COM port: {ComPort}", comPort);
                    ShowNotification(BarcodeResources.BarcodeSuccess, BarcodeResources.HubConnectionSuccess);
                    StartListening(comPort);
                }
                else
                {
                    _logger.LogError("Hub connection failed.");
                    ShowNotification(BarcodeResources.BarcodeError, BarcodeResources.HubConnectionFailed);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during barcode PublicInitialize");
                NotificationManager.ShowNotification("BarcodeError", "BarcodeExceptionMessage", "Barcode Error", "An exception occurred while initializing barcode.");
                throw;
            }
        }





        public void PopulateCOMPorts()
        {
            string[] comPorts = GetAvailableCOMPorts(); // Get available COM ports
            foreach (string port in comPorts)
            {
                _barcodeForm.comboBoxCOMPorts.Items.Add(port); // Add each port to the ComboBox
            }

            if (_barcodeForm.comboBoxCOMPorts.Items.Count > 0)
            {
                _barcodeForm.comboBoxCOMPorts.SelectedIndex = 0; // Set the first item as the default selection
            }

        }
        // Initialize and start HubConnection

        // Method to set the TextBox with the port connected to the barcode scanner
        public void SetBarcodeScannerPort()
        {
            string connectedPort = GetConnectedBarcodePort();
            _barcodeForm.comPortTextBox.Text = connectedPort; // Update the TextBox with the connected port

            if (connectedPort != "No scanner connected")
            {
                Console.WriteLine($"Barcode scanner connected on {connectedPort}");
            }
            else
            {
                Console.WriteLine("No barcode scanner detected.");
            }

        }


        // Method to get available COM ports
        public string[] GetAvailableCOMPorts()
        {
            // Get all available COM ports
            return SerialPort.GetPortNames();
        }

        // Method to detect which COM port is connected to the barcode scanner
        public string GetConnectedBarcodePort()
        {
            foreach (string port in GetAvailableCOMPorts())
            {
                if (IsBarcodeScannerConnected(port))
                {
                    return port;
                }
            }
            return "No scanner connected";
        }

        // Method to check if a barcode scanner is connected to a specific port
        public bool IsBarcodeScannerConnected(string portName)
        {
            SerialPort serialPort = null;

            try
            {
                // Initialize SerialPort with the provided port name
                serialPort = new SerialPort(portName, 9600, Parity.None, 8, StopBits.One);


                if (!serialPort.IsOpen)
                {
                    serialPort.Open();  // Open the port if it is not already open
                }

                serialPort.DiscardInBuffer();  // Clear any input buffer data
                serialPort.DiscardOutBuffer(); // Clear any output buffer data

                // Wait for a small delay to allow data to accumulate, if any
                System.Threading.Thread.Sleep(500);

                string response = serialPort.ReadExisting(); // Read the data from the port

                // Optionally, retry reading in case the first read didn't catch anything
                if (string.IsNullOrEmpty(response))
                {
                    System.Threading.Thread.Sleep(500); // Wait a little more
                    response = serialPort.ReadExisting(); // Try reading again
                }

                if (!string.IsNullOrEmpty(response))
                {
                    // If we received some data, we assume the barcode scanner is connected
                    return true;
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"Access to the port {portName} is denied: {ex.Message}");
                return true;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"I/O error on port {portName}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking port {portName}: {ex.Message}");
            }
            finally
            {
                // Always ensure the port is closed after the check, unless the port is managed elsewhere
                if (serialPort != null && serialPort.IsOpen)
                {
                    serialPort.Close();
                }
            }

            // If no data is received or an error occurred, assume the scanner is not connected
            return false;
        }



        [PluginConfiguration("ComPort")]
        public string GetCOMPort()
        {
            return _configuration.GetSection("Barcode").GetValue<string>("ComPort");
        }

        [PluginConfiguration("BarcodeBaseUrl")]
        public string GetBarcodeBaseUrl()
        {
            return _configuration.GetSection("Barcode").GetValue<string>("BarcodeBaseUrl");
        }






        #region Barcode Methods (Moved from SerialPortManager)

        public bool IsPortOpen => _serialPort != null && _serialPort.IsOpen;

        public void StartListening(string comPort = null)
        {
            try
            {
                if (IsPortOpen)
                {
                    // Already open: consider logging or handling this case
                    return;
                }
                // Check if the specified COM port is available
                if (!Array.Exists(SerialPort.GetPortNames(), port => port.Equals(comPort, StringComparison.OrdinalIgnoreCase)))
                {
                    Console.WriteLine($"COM port {comPort} is not available.");
                    return;
                }
                // Force release the port
                ForceReleasePort();
                try
                {
                    // Create a new SerialPort instance
                    _serialPort = new SerialPort(comPort, 9600, Parity.None, 8, StopBits.One);
                    // Subscribe to the DataReceived event
                    _serialPort.DataReceived += SerialPort_DataReceived;

                    // Open the serial port
                    _serialPort.Open();
                    return; // Success
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error opening COM port: {ex.Message}");
                    return;
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        public async Task<bool> InitializeHubConnection(string hubUrl)
        {
            if (string.IsNullOrWhiteSpace(hubUrl))
                throw new ArgumentException("Hub URL cannot be null or empty", nameof(hubUrl));

            try
            {
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(hubUrl)
                    .Build();

                // Add automatic reconnection
                _hubConnection.Closed += async (error) =>
                {
                    await Task.Delay(new Random().Next(0, 5) * 1000);
                    await _hubConnection.StartAsync();
                };

                await _hubConnection.StartAsync();
                Console.WriteLine("Successfully connected to Hub");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to connect to Hub: {ex.Message}");
                return false;  // Re-throw to allow caller to handle
            }
        }

        private async void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (sender is SerialPort port)
                {
                    // Check if capture image mode is active
                    if (_isCaptureImageMode)
                    {
                        SetCaptureImageMode(false);
                        // If no bytes are available, exit
                        if (port.BytesToRead <= 0)
                            return;

                        // Sleep for a brief moment to ensure data is fully received
                        Thread.Sleep(100);

                        byte[] imgData = new byte[port.BytesToRead];
                        port.Read(imgData, 0, imgData.Length); // Read the data as bytes

                        // Convert the byte array to a Base64 string
                        LastScannedBase64Data = imgCapture.GetImageAsBase64(imgData); // Update the last scanned Base64 data

                        if (LastScannedBase64Data != null)
                        {
                            // If you need to show the image in a PictureBox, you can still do that
                            Image img = imgCapture.GetbarcodeScannerImage(imgData);
                            if (img != null)
                            {
                                // Use NotificationManager instead of direct event
                                NotificationManager.NotifyBarcodeImageCaptured(img);
                            }
                        }
                        //SerialPortManager.Instance.StartListening(_comPort);
                        _serialPort.DataReceived += SerialPort_DataReceived;

                    }
                    else
                    {
                        // Barcode reading logic
                        byte[] dataBytes = new byte[port.BytesToRead];
                        port.Read(dataBytes, 0, dataBytes.Length);
                        string data = DecodeWindows1256(dataBytes);
                        if (!string.IsNullOrWhiteSpace(data))
                        {
                            // Check the connection and send data
                            if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                            {
                                Console.WriteLine("data " + data);
                                await _hubConnection.InvokeAsync("SendMessage", data);
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading data: {ex.Message}", "Error");
            }
        }

        public Task CaptureImageAsync(string comPort)
        {
            // Construct the command to capture the image
            string command = "\x16M\r" + imgCapture.hwPictureCmd;
            // Write the command to the serial port
            Write(command);
            return Task.CompletedTask;

        }

        public string DecodeWindows1256(byte[] windows1256Bytes)
        {
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                Encoding windows1256 = Encoding.GetEncoding("windows-1256");
                return windows1256.GetString(windows1256Bytes);
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        public void ForceReleasePort()
        {
            if (_serialPort != null)
            {
                try
                {
                    // Unsubscribe from the event
                    _serialPort.DataReceived -= SerialPort_DataReceived;

                    // Close the port if it's open
                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                    }

                    // Use reflection to access the BaseStream and force it to close
                    var baseStream = _serialPort.BaseStream;
                    if (baseStream != null)
                    {
                        baseStream.Close();
                        baseStream.Dispose();
                    }

                    // Dispose of the SerialPort
                    _serialPort.Dispose();
                    _serialPort = null; // Set to null to avoid reuse
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error releasing COM port: {ex.Message}");
                }
            }
        }

        public void SetCaptureImageMode(bool mode)
        {
            _isCaptureImageMode = mode; // Set the current capturing mode
        }

        public void Write(string command)
        {
            if (_serialPort != null && _serialPort.IsOpen)
            {
                _serialPort.Write(command);
            }
            else
            {
                throw new InvalidOperationException("Cannot write to the port because it is not open.");
            }
        }



        #endregion

    }
}
